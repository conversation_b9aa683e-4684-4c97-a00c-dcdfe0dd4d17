#!/usr/bin/env node

import { createServer } from 'http'
import { once } from 'events'
import { Sender } from '../app/utils/sender/sender'

async function timeoutDebug() {
    console.log('🔍 Timeout Debug Test\n')
    
    // Setup test server
    const server = createServer((req, res) => {
        const url = new URL(req.url!, `http://localhost:${(server.address() as any)?.port}`)
        console.log(`📡 Server received: ${req.method} ${url.pathname}`)
        
        if (url.pathname === '/timeout') {
            console.log('⏰ Timeout endpoint - not responding (simulating hang)')
            // Don't respond to simulate timeout
            return
        }
        
        res.writeHead(200, { 'Content-Type': 'application/json' })
        res.end(JSON.stringify({ message: 'success' }))
    })
    
    server.listen(0)
    await once(server, 'listening')
    const port = (server.address() as any)?.port
    console.log(`📡 Test server running on port ${port}\n`)
    
    // Test 1: Basic timeout with Sender
    console.log('🧪 Test 1: Basic Sender timeout (100ms)')
    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            timeout: 100
        })
        
        const start = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
        console.log('🔍 Error cause name:', (error as any).cause?.name)
        console.log('🔍 Error cause message:', (error as any).cause?.message)
    }
    console.log('')
    
    // Test 2: AbortSignal.timeout directly
    console.log('🧪 Test 2: AbortSignal.timeout directly (100ms)')
    try {
        const signal = AbortSignal.timeout(100)
        const sender = new Sender(`http://localhost:${port}/timeout`)
        
        const start = Date.now()
        await sender.send(null, { signal })
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ AbortSignal timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
    }
    console.log('')
    
    // Test 3: Very short timeout
    console.log('🧪 Test 3: Very short timeout (10ms)')
    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            timeout: 10
        })
        
        const start = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Short timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
    }
    console.log('')
    
    // Test 4: Undici pool timeout options
    console.log('🧪 Test 4: Undici pool timeout options')
    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            pool: {
                headersTimeout: 100,
                bodyTimeout: 100
            }
        })
        
        const start = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Pool timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
    }
    console.log('')
    
    // Test 5: Manual AbortController
    console.log('🧪 Test 5: Manual AbortController (100ms)')
    try {
        const controller = new AbortController()
        const sender = new Sender(`http://localhost:${port}/timeout`)
        
        setTimeout(() => {
            console.log('🛑 Manually aborting...')
            controller.abort()
        }, 100)
        
        const start = Date.now()
        await sender.send(null, { signal: controller.signal })
        console.log('❌ ERROR: Should have been aborted!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Manual abort after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
    }
    console.log('')
    
    // Test 6: Check AbortSignal.any behavior
    console.log('🧪 Test 6: AbortSignal.any behavior')
    try {
        const timeoutSignal = AbortSignal.timeout(100)
        const manualController = new AbortController()
        const combinedSignal = AbortSignal.any([timeoutSignal, manualController.signal])
        
        const sender = new Sender(`http://localhost:${port}/timeout`)
        
        const start = Date.now()
        await sender.send(null, { signal: combinedSignal })
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Combined signal timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
    }
    console.log('')
    
    // Cleanup
    server.close()
    await once(server, 'close')
    console.log('🧹 Test server closed')
}

timeoutDebug().catch(console.error)
