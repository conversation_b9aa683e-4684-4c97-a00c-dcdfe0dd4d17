#!/usr/bin/env node

import { createServer } from 'http'
import { once } from 'events'
import { Pool, request } from 'undici'

async function testRawUndici() {
    console.log('🔍 Testing Raw Undici Timeout Behavior\n')
    
    // Setup test server
    const server = createServer((req, res) => {
        console.log(`📡 Server received: ${req.method} ${req.url}`)
        
        if (req.url?.includes('/timeout')) {
            console.log('⏰ Timeout endpoint - not responding')
            // Don't respond to simulate timeout
            return
        }
        
        res.writeHead(200, { 'Content-Type': 'application/json' })
        res.end(JSON.stringify({ message: 'success' }))
    })
    
    server.listen(0)
    await once(server, 'listening')
    const port = (server.address() as any)?.port
    const origin = `http://localhost:${port}`
    console.log(`📡 Test server running on port ${port}\n`)
    
    // Test 1: Raw undici request with AbortSignal.timeout
    console.log('🧪 Test 1: Raw undici.request() with AbortSignal.timeout(100)')
    try {
        const signal = AbortSignal.timeout(100)
        const start = Date.now()
        
        await request(`${origin}/timeout`, { signal })
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Raw undici timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
    }
    console.log('')
    
    // Test 2: Raw Pool with AbortSignal.timeout
    console.log('🧪 Test 2: Raw Pool with AbortSignal.timeout(100)')
    try {
        const pool = new Pool(origin)
        const signal = AbortSignal.timeout(100)
        const start = Date.now()
        
        await pool.request({
            path: '/timeout',
            method: 'POST',
            signal
        })
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Raw Pool timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
    }
    console.log('')
    
    // Test 3: Pool with headersTimeout/bodyTimeout
    console.log('🧪 Test 3: Pool with headersTimeout=100, bodyTimeout=100')
    try {
        const pool = new Pool(origin, {
            headersTimeout: 100,
            bodyTimeout: 100
        })
        const start = Date.now()
        
        await pool.request({
            path: '/timeout',
            method: 'POST'
        })
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Pool timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
    }
    console.log('')
    
    // Test 4: Manual AbortController
    console.log('🧪 Test 4: Manual AbortController after 100ms')
    try {
        const pool = new Pool(origin)
        const controller = new AbortController()
        
        setTimeout(() => {
            console.log('🛑 Manually aborting...')
            controller.abort()
        }, 100)
        
        const start = Date.now()
        
        await pool.request({
            path: '/timeout',
            method: 'POST',
            signal: controller.signal
        })
        console.log('❌ ERROR: Should have been aborted!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Manual abort after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
    }
    console.log('')
    
    // Test 5: Successful request for comparison
    console.log('🧪 Test 5: Successful request')
    try {
        const pool = new Pool(origin)
        const start = Date.now()
        
        const response = await pool.request({
            path: '/success',
            method: 'POST'
        })
        
        const elapsed = Date.now() - start
        console.log(`✅ Successful request after: ${elapsed}ms`)
        console.log('🔍 Status:', response.statusCode)
        
        // Read body
        const body = await response.body.text()
        console.log('🔍 Body:', body)
    } catch (error) {
        console.log('❌ ERROR: Successful request failed:', (error as Error).message)
    }
    console.log('')
    
    // Cleanup
    server.close()
    await once(server, 'close')
    console.log('🧹 Test server closed')
}

testRawUndici().catch(console.error)
