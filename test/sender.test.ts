#!/usr/bin/env node

import type { SenderRequestError } from '../app/utils/sender/errors/sender-request-error'
import { strict as assert } from 'node:assert'
import { once } from 'node:events'
import { createServer, type IncomingMessage, type Server, type ServerResponse } from 'node:http'
import { HttpMethod } from '../app/utils/sender/constants'
import { Sender } from '../app/utils/sender/sender'

interface TestCase {
    name: string
    fn: () => Promise<void>
}

class TestRunner {
    private readonly tests: TestCase[] = []
    private passed = 0
    private failed = 0
    private server: Server | null = null
    private serverPort: number | null = null

    test(name: string, fn: () => Promise<void>): void {
        this.tests.push({ name, fn })
    }

    async setupServer(): Promise<void> {
        this.server = createServer((req: IncomingMessage, res: ServerResponse) => {
            const url = new URL(req.url!, `http://localhost:${this.serverPort}`)
            const path = url.pathname
            const delay = Number.parseInt(url.searchParams.get('delay') || '0')

            setTimeout(() => {
                switch (path) {
                    case '/success':
                        res.writeHead(200, { 'Content-Type': 'application/json' })
                        res.end(JSON.stringify({ message: 'success', method: req.method }))
                        break
                    case '/error':
                        res.writeHead(500, { 'Content-Type': 'application/json' })
                        res.end(JSON.stringify({ error: 'Internal Server Error' }))
                        break
                    case '/timeout':
                    // Don't respond to simulate timeout

                        break

                    case '/retry': {
                        const attempt = Number.parseInt(url.searchParams.get('attempt') || '1')

                        if (attempt < 3) {
                            res.writeHead(500, { 'Content-Type': 'application/json' })
                            res.end(JSON.stringify({ error: 'Retry me', attempt }))
                        } else {
                            res.writeHead(200, { 'Content-Type': 'application/json' })
                            res.end(JSON.stringify({ message: 'success after retry', attempt }))
                        }

                        break
                    }

                    case '/echo': {
                        let body = ''
                        req.on('data', (chunk) => body += chunk)

                        req.on('end', () => {
                            res.writeHead(200, { 'Content-Type': 'application/json' })

                            res.end(JSON.stringify({
                                method: req.method,
                                headers: req.headers,
                                body: body || null,
                            }))
                        })

                        break
                    }

                    case '/large': {
                        const size = Number.parseInt(url.searchParams.get('size') || '1000')
                        res.writeHead(200, { 'Content-Type': 'text/plain' })
                        res.end('x'.repeat(size))
                        break
                    }

                    case '/json':
                        res.writeHead(200, { 'Content-Type': 'application/json' })
                        res.end(JSON.stringify({ data: 'test', timestamp: Date.now() }))
                        break
                    default:
                        res.writeHead(404, { 'Content-Type': 'application/json' })
                        res.end(JSON.stringify({ error: 'Not Found' }))
                }
            }, delay)
        })

        this.server.listen(0)
        await once(this.server, 'listening')
        this.serverPort = (this.server.address() as any)?.port
    }

    async teardownServer(): Promise<void> {
        if (this.server) {
            this.server.close()
            await once(this.server, 'close')
        }
    }

    async run(): Promise<void> {
        console.log('🧪 Starting Sender Tests...\n')

        await this.setupServer()
        console.log(`📡 Test server running on port ${this.serverPort}\n`)

        for (const { name, fn } of this.tests) {
            try {
                console.log(`🔍 ${name}`)
                await fn()
                this.passed++
                console.log(`✅ PASSED\n`)
            } catch (error) {
                this.failed++
                console.log(`❌ FAILED: ${(error as Error).message}\n`)
                console.error((error as Error).stack)
            }
        }

        await this.teardownServer()

        console.log(`\n📊 Test Results:`)
        console.log(`✅ Passed: ${this.passed}`)
        console.log(`❌ Failed: ${this.failed}`)
        console.log(`📈 Total: ${this.tests.length}`)

        if (this.failed > 0) {
            process.exit(1)
        }
    }

    getServerUrl(path = '/'): string {
        return `http://localhost:${this.serverPort}${path}`
    }
}

const runner = new TestRunner()

// Constructor Tests
runner.test('Constructor - Valid URL parsing', async () => {
    const sender = new Sender('http://example.com/api/test')
    assert.equal(sender.origin, 'http://example.com')
    assert.equal(sender.path, '/api/test')
})

runner.test('Constructor - Invalid URL throws error', async () => {
    assert.throws(() => {
        new Sender('invalid-url')
    }, /Failed to parse URL/)
})

runner.test('Constructor - Default options', async () => {
    const sender = new Sender('http://example.com')
    assert.equal(sender.activeRequests, 0)
    assert.equal(typeof sender.connections, 'number')
})

runner.test('Constructor - Custom options', async () => {
    const sender = new Sender('http://example.com', {
        timeout: 5000,
        method: HttpMethod.GET,
        headers: { 'User-Agent': 'test' },
    })

    assert.equal(sender.origin, 'http://example.com')
})

// Basic Request Tests
runner.test('Basic GET request', async () => {
    const sender = new Sender(runner.getServerUrl('/success'))
    const response = await sender.send()

    assert.equal(response.status, 200)
    assert.equal(typeof response.id, 'string')
    assert.equal(typeof response.took, 'bigint')
    assert.ok(response.took > 0n)

    const body = JSON.parse(response.body)
    assert.equal(body.message, 'success')
})

runner.test('POST request with body', async () => {
    const sender = new Sender(runner.getServerUrl('/echo'), {
        method: HttpMethod.POST,
    })

    const testBody = JSON.stringify({ test: 'data' })
    const response = await sender.send(testBody)

    assert.equal(response.status, 200)
    const responseBody = JSON.parse(response.body)
    assert.equal(responseBody.method, 'POST')
    assert.equal(responseBody.body, testBody)
})

runner.test('Custom headers', async () => {
    const sender = new Sender(runner.getServerUrl('/echo'), {
        headers: { 'X-Test-Header': 'test-value' },
    })

    const response = await sender.send()
    const body = JSON.parse(response.body)
    assert.equal(body.headers['x-test-header'], 'test-value')
})

runner.test('Per-request options override', async () => {
    const sender = new Sender(runner.getServerUrl('/echo'), {
        method: HttpMethod.GET,
        headers: { 'X-Default': 'default' },
    })

    const response = await sender.send('test body', {
        method: HttpMethod.POST,
        headers: { 'X-Override': 'override' },
    })

    const body = JSON.parse(response.body)
    assert.equal(body.method, 'POST')
    assert.equal(body.headers['x-default'], 'default')
    assert.equal(body.headers['x-override'], 'override')
    assert.equal(body.body, 'test body')
})

// Event System Tests
runner.test('Event system - activeRequests', async () => {
    const sender = new Sender(runner.getServerUrl('/success'))
    const activeRequestsEvents: number[] = []

    sender.on('activeRequests', (count) => {
        activeRequestsEvents.push(count)
    })

    const promise = sender.send()
    await promise

    assert.deepEqual(activeRequestsEvents, [1, 0])
})

runner.test('Event system - request and response events', async () => {
    const sender = new Sender(runner.getServerUrl('/success'))
    let requestEvent: any = null
    let responseEvent: any = null

    sender.on('request', (req) => { requestEvent = req })
    sender.on('response', (res) => { responseEvent = res })

    await sender.send('test')

    assert.ok(requestEvent)
    assert.equal(requestEvent.body, 'test')
    assert.equal(typeof requestEvent.id, 'string')

    assert.ok(responseEvent)
    assert.equal(responseEvent.status, 200)
    assert.equal(responseEvent.id, requestEvent.id)
})

// Error Handling Tests
runner.test('HTTP error handling', async () => {
    const sender = new Sender(runner.getServerUrl('/error'))
    let errorEvent: any = null

    sender.on('error', (err) => { errorEvent = err })

    try {
        await sender.send()
        assert.fail('Should have thrown an error')
    } catch (error) {
        assert.ok((error as Error).message.includes('Request failed'))
        assert.ok(errorEvent)
        assert.equal(errorEvent.constructor.name, 'SenderRequestError')
    }
})

runner.test('Timeout handling', async () => {
    const sender = new Sender(runner.getServerUrl('/timeout'), {
        timeout: 100,
    })

    const start = Date.now()

    try {
        await sender.send()
        assert.fail('Should have thrown timeout error')
    } catch {
        const elapsed = Date.now() - start
        assert.ok(elapsed >= 100 && elapsed < 200, `Timeout should be ~100ms, got ${elapsed}ms`)
    }
})

runner.test('AbortSignal support', async () => {
    const sender = new Sender(runner.getServerUrl('/success?delay=1000'))
    const controller = new AbortController()

    setTimeout(() => controller.abort(), 100)

    try {
        await sender.send(null, { signal: controller.signal })
        assert.fail('Should have been aborted')
    } catch (error) {
        assert.ok((error as any).name === 'AbortError' || (error as Error).message.includes('aborted'))
    }
})

// Retry Logic Tests
runner.test('Retry on error - basic retry', async () => {
    let attemptCount = 0

    const sender = new Sender(runner.getServerUrl('/error'), {
        retry: {
            enabled: true,
            retries: 2,
            delay: 10,
            onFailedAttempt: (error, attempts, retriesLeft) => {
                attemptCount = attempts
            },
        },
    })

    try {
        await sender.send()
        assert.fail('Should have failed after retries')
    } catch (error) {
        assert.equal(attemptCount, 3) // Initial + 2 retries
        assert.ok((error as any).errors?.length === 3) // AggregateError with all attempts
    }
})

runner.test('Retry with shouldRetry condition', async () => {
    let shouldRetryCallCount = 0

    const sender = new Sender(runner.getServerUrl('/error'), {
        retry: {
            enabled: true,
            retries: 3,
            delay: 10,
            shouldRetry: (error) => {
                shouldRetryCallCount++

                return shouldRetryCallCount <= 2 // Only retry first 2 times
            },
        },
    })

    try {
        await sender.send()
        assert.fail('Should have failed')
    } catch {
        assert.equal(shouldRetryCallCount, 3)
    }
})

runner.test('Retry with shouldRetryOnResponse condition', async () => {
    let responseRetryCount = 0

    const sender = new Sender(runner.getServerUrl('/error'), {
        retry: {
            enabled: true,
            retries: 2,
            delay: 10,
            shouldRetryOnResponse: (response) => {
                responseRetryCount++

                return response.status >= 500 && responseRetryCount <= 1
            },
            onResponseAttempt: (response, attempts, retriesLeft) => {
                assert.equal(response.status, 500)
                assert.ok(attempts > 0)
            },
        },
    })

    try {
        await sender.send()
        assert.fail('Should have failed')
    } catch {
        assert.equal(responseRetryCount, 2)
    }
})

runner.test('Exponential backoff with jitter', async () => {
    const delays: number[] = []

    const sender = new Sender(runner.getServerUrl('/error'), {
        retry: {
            enabled: true,
            retries: 3,
            delay: 100,
            backoff: 2,
            jitter: 0.1,
            onFailedAttempt: async (error, attempts, retriesLeft) => {
                if (attempts > 1) {
                    const start = Date.now()
                    await new Promise((resolve) => setTimeout(resolve, 0))
                    delays.push(Date.now() - start)
                }
            },
        },
    })

    const start = Date.now()

    try {
        await sender.send()
        assert.fail('Should have failed')
    } catch {
        const totalTime = Date.now() - start
        assert.ok(totalTime >= 300, `Should take at least 300ms for exponential backoff, got ${totalTime}ms`)
    }
})

// Response Body Formatting Tests
runner.test('Custom response body formatter', async () => {
    interface CustomResponse {
        parsed: any
        timestamp: number
    }

    const sender = new Sender<CustomResponse>(runner.getServerUrl('/json'), {
        responseBodyFormatter: (body: string) => ({
            parsed: JSON.parse(body),
            timestamp: Date.now(),
        }),
    })

    const response = await sender.send()
    assert.equal(response.status, 200)
    assert.ok(typeof response.body.parsed === 'object')
    assert.ok(typeof response.body.timestamp === 'number')
    assert.ok(response.body.parsed.data === 'test')
})

// Concurrent Requests Tests
runner.test('Concurrent requests handling', async () => {
    const sender = new Sender(runner.getServerUrl('/success?delay=100'))
    const activeRequestsCounts: number[] = []

    sender.on('activeRequests', (count) => {
        activeRequestsCounts.push(count)
    })

    const promises = [
        sender.send(),
        sender.send(),
        sender.send(),
    ]

    await Promise.all(promises)

    // Should see: 1, 2, 3, 2, 1, 0 (or similar pattern)
    assert.ok(Math.max(...activeRequestsCounts) === 3)
    assert.ok(activeRequestsCounts.at(-1) === 0)
})

// Connection Pool Tests
runner.test('Connection reuse', async () => {
    const sender = new Sender(runner.getServerUrl('/success'))
    const connectionCounts: number[] = []

    sender.on('connections', (count) => {
        connectionCounts.push(count)
    })

    // Make multiple requests to same endpoint
    await sender.send()
    await sender.send()
    await sender.send()

    // Should reuse connections
    assert.ok(connectionCounts.length > 0)
    assert.ok(connectionCounts.some((count) => count > 0))
})

// DNS Cache Tests
runner.test('DNS cache enabled', async () => {
    const sender = new Sender(runner.getServerUrl('/success'), {
        dns: { enabled: true },
    })

    const response = await sender.send()
    assert.equal(response.status, 200)
})

runner.test('DNS cache disabled', async () => {
    const sender = new Sender(runner.getServerUrl('/success'), {
        dns: { enabled: false },
    })

    const response = await sender.send()
    assert.equal(response.status, 200)
})

// Large Payload Tests
runner.test('Large response handling', async () => {
    const sender = new Sender(runner.getServerUrl('/large?size=10000'))
    const response = await sender.send()

    assert.equal(response.status, 200)
    assert.equal(response.body.length, 10_000)
    assert.ok(response.body === 'x'.repeat(10_000))
})

// Metadata Tests
runner.test('Request metadata', async () => {
    const sender = new Sender(runner.getServerUrl('/success'))
    let requestEvent: any = null
    let responseEvent: any = null

    sender.on('request', (req) => { requestEvent = req })
    sender.on('response', (res) => { responseEvent = res })

    const metadata = { userId: 123, requestType: 'test' }
    await sender.send(null, { metadata })

    assert.deepEqual(requestEvent.metadata, metadata)
    assert.deepEqual(responseEvent.metadata, metadata)
})

// HTTP Methods Tests
runner.test('Different HTTP methods', async () => {
    const methods = [HttpMethod.GET, HttpMethod.POST, HttpMethod.PUT, HttpMethod.DELETE, HttpMethod.PATCH]

    for (const method of methods) {
        const sender = new Sender(runner.getServerUrl('/echo'), { method })
        const response = await sender.send('test')

        const body = JSON.parse(response.body)
        assert.equal(body.method, method)
    }
})

// Edge Cases Tests
runner.test('Null and undefined body handling', async () => {
    const sender = new Sender(runner.getServerUrl('/echo'))

    const response1 = await sender.send(null)
    const body1 = JSON.parse(response1.body)
    assert.equal(body1.body, null)

    const response2 = await sender.send()
    const body2 = JSON.parse(response2.body)
    assert.equal(body2.body, null)
})

runner.test('Empty string body', async () => {
    const sender = new Sender(runner.getServerUrl('/echo'))
    const response = await sender.send('')

    const body = JSON.parse(response.body)
    assert.equal(body.body, '')
})

runner.test('Request ID uniqueness', async () => {
    const sender = new Sender(runner.getServerUrl('/success'))
    const ids = new Set<string>()

    for (let i = 0; i < 10; i++) {
        const response = await sender.send()
        assert.ok(!ids.has(response.id), `Duplicate ID found: ${response.id}`)
        ids.add(response.id)
    }
})

// Error Context Tests
runner.test('Error context preservation', async () => {
    const sender = new Sender(runner.getServerUrl('/error'))

    try {
        await sender.send('test-body', {
            headers: { 'X-Test': 'test-value' },
            metadata: { testId: 'abc123' },
        })

        assert.fail('Should have thrown an error')
    } catch (error) {
        const senderError = error as SenderRequestError
        assert.ok(senderError.request)
        assert.equal(senderError.request.body, 'test-body')
        assert.equal(senderError.request.headers['X-Test'], 'test-value')
        assert.deepEqual(senderError.request.metadata, { testId: 'abc123' })
        assert.ok(senderError.undiciResponse)
        assert.ok(senderError.response)
    }
})

// Performance Tests
runner.test('Response timing accuracy', async () => {
    const sender = new Sender(runner.getServerUrl('/success?delay=100'))

    const start = process.hrtime.bigint()
    const response = await sender.send()
    const end = process.hrtime.bigint()

    const actualTime = Number(end - start) / 1_000_000 // Convert to ms
    const reportedTime = Number(response.took) / 1_000_000 // Convert to ms

    // Allow 10ms tolerance
    assert.ok(
        Math.abs(actualTime - reportedTime) < 10,
        `Timing mismatch: actual=${actualTime}ms, reported=${reportedTime}ms`,
    )
})

// Advanced Edge Cases Tests
runner.test('Invalid JSON response handling', async () => {
    // Add invalid JSON endpoint to server
    const originalHandler = runner.server?.listeners('request')[0] as any
    runner.server?.removeAllListeners('request')

    runner.server?.on('request', (req: IncomingMessage, res: ServerResponse) => {
        const url = new URL(req.url!, `http://localhost:${runner.serverPort}`)

        if (url.pathname === '/invalid-json') {
            res.writeHead(200, { 'Content-Type': 'application/json' })
            res.end('{ invalid json }')

            return
        }

        originalHandler(req, res)
    })

    const sender = new Sender<any>(runner.getServerUrl('/invalid-json'), {
        responseBodyFormatter: (body: string) => {
            try {
                return JSON.parse(body)
            } catch {
                return { error: 'Invalid JSON', raw: body }
            }
        },
    })

    const response = await sender.send()
    assert.equal(response.status, 200)
    assert.equal(response.body.error, 'Invalid JSON')
    assert.equal(response.body.raw, '{ invalid json }')
})

runner.test('Pool options configuration', async () => {
    const sender = new Sender(runner.getServerUrl('/success'), {
        pool: {
            connections: 5,
            pipelining: 1,
            keepAliveTimeout: 4000,
            keepAliveMaxTimeout: 600_000,
        },
    })

    const response = await sender.send()
    assert.equal(response.status, 200)
})

runner.test('Complex retry scenario with mixed conditions', async () => {
    let errorRetryCount = 0
    let responseRetryCount = 0

    const sender = new Sender(runner.getServerUrl('/error'), {
        retry: {
            enabled: true,
            retries: 5,
            delay: 10,
            backoff: 1.5,
            jitter: 0.2,
            maxDelay: 1000,
            shouldRetry: (error) => {
                errorRetryCount++

                return errorRetryCount <= 3
            },
            shouldRetryOnResponse: (response) => {
                responseRetryCount++

                return response.status >= 500 && responseRetryCount <= 2
            },
            onFailedAttempt: (error, attempts, retriesLeft) => {
                assert.ok(attempts > 0)
                assert.ok(retriesLeft >= 0)
            },
            onResponseAttempt: (response, attempts, retriesLeft) => {
                assert.equal(response.status, 500)
                assert.ok(attempts > 0)
            },
        },
    })

    try {
        await sender.send()
        assert.fail('Should have failed')
    } catch {
        assert.ok(errorRetryCount > 0)
        assert.ok(responseRetryCount > 0)
    }
})

runner.test('Memory usage with large number of requests', async () => {
    const sender = new Sender(runner.getServerUrl('/success'))
    const promises: Array<Promise<any>> = []

    // Create 50 concurrent requests
    for (let i = 0; i < 50; i++) {
        promises.push(sender.send(`request-${i}`))
    }

    const responses = await Promise.all(promises)

    assert.equal(responses.length, 50)

    responses.forEach((response, index) => {
        assert.equal(response.status, 200)
        assert.ok(response.id)
    })

    // Verify activeRequests is back to 0
    assert.equal(sender.activeRequests, 0)
})

runner.test('Request cancellation with multiple AbortSignals', async () => {
    const sender = new Sender(runner.getServerUrl('/success?delay=1000'))
    const controller1 = new AbortController()
    const controller2 = new AbortController()

    // Create combined signal
    const combinedSignal = AbortSignal.any([controller1.signal, controller2.signal])

    setTimeout(() => controller2.abort(), 100)

    try {
        await sender.send(null, { signal: combinedSignal })
        assert.fail('Should have been aborted')
    } catch (error) {
        assert.ok((error as any).name === 'AbortError' || (error as Error).message.includes('aborted'))
    }
})

runner.test('Headers case sensitivity and merging', async () => {
    const sender = new Sender(runner.getServerUrl('/echo'), {
        headers: {
            'Content-Type': 'application/json',
            'X-Default-Header': 'default-value',
            'authorization': 'Bearer token1',
        },
    })

    const response = await sender.send('test', {
        headers: {
            'X-Override-Header': 'override-value',
            'Authorization': 'Bearer token2', // Different case
            'content-type': 'text/plain', // Different case
        },
    })

    const body = JSON.parse(response.body)
    const headers = body.headers

    // Check that headers are properly merged and case is handled
    assert.ok(headers['x-default-header'] === 'default-value')
    assert.ok(headers['x-override-header'] === 'override-value')
    assert.ok(headers['authorization'] === 'Bearer token2') // Should be overridden
    assert.ok(headers['content-type'] === 'text/plain') // Should be overridden
})

runner.test('Response header parsing', async () => {
    // Add custom headers endpoint
    const originalHandler = runner.server?.listeners('request')[0] as any
    runner.server?.removeAllListeners('request')

    runner.server?.on('request', (req: IncomingMessage, res: ServerResponse) => {
        const url = new URL(req.url!, `http://localhost:${runner.serverPort}`)

        if (url.pathname === '/custom-headers') {
            res.writeHead(200, {
                'Content-Type': 'application/json',
                'X-Custom-Header': 'custom-value',
                'Set-Cookie': ['session=abc123', 'theme=dark'],
                'Cache-Control': 'no-cache, no-store',
            })

            res.end(JSON.stringify({ message: 'headers test' }))

            return
        }

        originalHandler(req, res)
    })

    const sender = new Sender(runner.getServerUrl('/custom-headers'))
    const response = await sender.send()

    assert.equal(response.status, 200)
    assert.equal(response.headers['content-type'], 'application/json')
    assert.equal(response.headers['x-custom-header'], 'custom-value')
    assert.equal(response.headers['set-cookie'], 'session=abc123,theme=dark')
    assert.equal(response.headers['cache-control'], 'no-cache, no-store')
})

runner.test('DNS cache options configuration', async () => {
    const sender1 = new Sender(runner.getServerUrl('/success'), {
        dns: true, // Simple boolean
    })

    const sender2 = new Sender(runner.getServerUrl('/success'), {
        dns: {
            enabled: true,
            maxTTL: 300,
            maxItems: 100,
        },
    })

    const sender3 = new Sender(runner.getServerUrl('/success'), {
        dns: false, // Disabled
    })

    const [response1, response2, response3] = await Promise.all([
        sender1.send(),
        sender2.send(),
        sender3.send(),
    ])

    assert.equal(response1.status, 200)
    assert.equal(response2.status, 200)
    assert.equal(response3.status, 200)
})

runner.test('Error handling with custom error types', async () => {
    const sender = new Sender('http://non-existent-domain-12345.com/test', {
        timeout: 1000,
    })

    try {
        await sender.send()
        assert.fail('Should have thrown an error')
    } catch (error) {
        const senderError = error as SenderRequestError
        assert.ok(senderError instanceof SenderRequestError)
        assert.ok(senderError.message.includes('Request failed'))
        assert.ok(senderError.cause) // Should have original error as cause
        assert.ok(senderError.timestamp instanceof Date)
    }
})

runner.test('Stress test - rapid sequential requests', async () => {
    const sender = new Sender(runner.getServerUrl('/success'))
    const results: any[] = []

    // Send 100 requests sequentially as fast as possible
    for (let i = 0; i < 100; i++) {
        const response = await sender.send(`request-${i}`)
        results.push(response)
    }

    assert.equal(results.length, 100)

    // Verify all requests completed successfully
    results.forEach((response, index) => {
        assert.equal(response.status, 200)
        assert.ok(response.id)
        assert.ok(response.took > 0n)
    })

    // Verify all IDs are unique
    const ids = new Set(results.map((r) => r.id))
    assert.equal(ids.size, 100)
})

console.log('🚀 Running Comprehensive Sender Tests...')
console.log('📋 Test Categories:')
console.log('  • Constructor & Configuration')
console.log('  • Basic HTTP Operations')
console.log('  • Event System')
console.log('  • Error Handling')
console.log('  • Retry Logic')
console.log('  • Response Formatting')
console.log('  • Concurrency & Performance')
console.log('  • Connection Pooling')
console.log('  • DNS Caching')
console.log('  • Edge Cases & Stress Tests')
console.log('')

// Run tests
runner.run().catch(console.error)
