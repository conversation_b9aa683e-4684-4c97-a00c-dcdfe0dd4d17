#!/usr/bin/env node

import { once } from 'node:events'
import { createServer, type IncomingMessage, type ServerResponse } from 'node:http'
import { Sender } from '../app/utils/sender/sender'

async function debugTest() {
    console.log('🔍 Debug Test - Analyzing Sender Issues\n')

    // Setup test server
    const server = createServer((req: IncomingMessage, res: ServerResponse) => {
        const url = new URL(req.url!, `http://localhost:${(server.address() as any)?.port}`)
        const path = url.pathname

        console.log(`📡 Server received: ${req.method} ${path}`)

        if (path === '/error') {
            res.writeHead(500, { 'Content-Type': 'application/json' })
            res.end(JSON.stringify({ error: 'Internal Server Error' }))
        } else if (path === '/timeout') {
            // Don't respond to simulate timeout
            console.log('⏰ Timeout endpoint - not responding')
        } else {
            res.writeHead(200, { 'Content-Type': 'application/json' })
            res.end(JSON.stringify({ message: 'success' }))
        }
    })

    server.listen(0)
    await once(server, 'listening')
    const port = (server.address() as any)?.port
    console.log(`📡 Test server running on port ${port}\n`)

    // Test 1: HTTP Error Handling
    console.log('🧪 Test 1: HTTP Error Handling')

    try {
        const sender = new Sender(`http://localhost:${port}/error`)
        let errorEventEmitted = false

        sender.on('error', (err) => {
            console.log('❌ Error event emitted:', err.constructor.name)
            errorEventEmitted = true
        })

        await sender.send()
        console.log('❌ ERROR: Should have thrown an error!')
    } catch (error) {
        console.log('✅ Exception caught:', (error as Error).message)
        console.log('🔍 Error type:', (error as any).constructor.name)
        console.log('🔍 Has cause:', !!(error as any).cause)
    }

    console.log('')

    // Test 2: Retry Logic
    console.log('🧪 Test 2: Retry Logic')

    try {
        let attemptCount = 0

        const sender = new Sender(`http://localhost:${port}/error`, {
            retry: {
                enabled: true,
                retries: 2,
                delay: 10,
                onFailedAttempt: (error, attempts, retriesLeft) => {
                    attemptCount = attempts
                    console.log(`🔄 Retry attempt ${attempts}, retries left: ${retriesLeft}`)
                },
            },
        })

        await sender.send()
        console.log('❌ ERROR: Should have failed after retries!')
    } catch (error) {
        console.log('✅ Final error after retries:', (error as Error).message)
        console.log('🔍 Error type:', (error as any).constructor.name)
        console.log('🔍 Is AggregateError:', error instanceof AggregateError)

        if (error instanceof AggregateError) {
            console.log('🔍 Number of errors:', error.errors.length)
        }
    }

    console.log('')

    // Test 3: Timeout Behavior
    console.log('🧪 Test 3: Timeout Behavior')

    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            timeout: 100,
        })

        const startTime = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - startTime
        console.log('✅ Timeout error after:', `${elapsed}ms`)
        console.log('🔍 Error message:', (error as Error).message)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error cause:', (error as any).cause?.message)
        console.log('🔍 Error cause name:', (error as any).cause?.name)
    }

    console.log('')

    // Test 4: AbortSignal
    console.log('🧪 Test 4: AbortSignal')

    try {
        const sender = new Sender(`http://localhost:${port}/success`)
        const controller = new AbortController()

        setTimeout(() => {
            console.log('🛑 Aborting request...')
            controller.abort()
        }, 50)

        await sender.send(null, { signal: controller.signal })
        console.log('❌ ERROR: Should have been aborted!')
    } catch (error) {
        console.log('✅ Abort error:', (error as Error).message)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Is AbortError:', (error as any).name === 'AbortError')
    }

    console.log('')

    // Test 5: Response vs Error Handling
    console.log('🧪 Test 5: Response vs Error Handling')

    try {
        const sender = new Sender(`http://localhost:${port}/error`, {
            retry: {
                enabled: true,
                retries: 1,
                shouldRetryOnResponse: (response) => {
                    console.log('🔍 shouldRetryOnResponse called with status:', response.status)

                    return response.status >= 500
                },
            },
        })

        await sender.send()
        console.log('❌ ERROR: Should have failed!')
    } catch (error) {
        console.log('✅ Error after response retry:', (error as Error).message)
    }

    console.log('')

    // Test 6: Detailed Timeout Analysis
    console.log('🧪 Test 6: Detailed Timeout Analysis')

    // Test với timeout rất ngắn
    console.log('📍 Testing with 50ms timeout...')

    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            timeout: 50,
        })

        const startTime = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - startTime
        console.log(`✅ 50ms timeout error after: ${elapsed}ms`)

        console.log('🔍 Error details:', {
            name: (error as any).name,
            message: (error as Error).message,
            cause: (error as any).cause?.name,
            causeMessage: (error as any).cause?.message,
        })
    }

    // Test với AbortSignal.timeout trực tiếp
    console.log('📍 Testing AbortSignal.timeout directly...')

    try {
        const signal = AbortSignal.timeout(100)
        const sender = new Sender(`http://localhost:${port}/timeout`)

        const startTime = Date.now()
        await sender.send(null, { signal })
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - startTime
        console.log(`✅ AbortSignal.timeout error after: ${elapsed}ms`)

        console.log('🔍 Error details:', {
            name: (error as any).name,
            message: (error as Error).message,
            cause: (error as any).cause?.name,
        })
    }

    // Test undici timeout behavior
    console.log('📍 Testing undici pool timeout options...')

    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            pool: {
                headersTimeout: 100,
                bodyTimeout: 100,
            },
        })

        const startTime = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - startTime
        console.log(`✅ Pool timeout error after: ${elapsed}ms`)

        console.log('🔍 Error details:', {
            name: (error as any).name,
            message: (error as Error).message,
            cause: (error as any).cause?.name,
        })
    }

    console.log('')

    // Cleanup
    server.close()
    await once(server, 'close')
    console.log('🧹 Test server closed')
}

debugTest().catch(console.error)
