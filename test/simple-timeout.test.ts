#!/usr/bin/env node

import { createServer } from 'http'
import { once } from 'events'

async function testAbortSignalTimeout() {
    console.log('🔍 Testing AbortSignal.timeout behavior\n')
    
    // Test 1: AbortSignal.timeout alone
    console.log('🧪 Test 1: AbortSignal.timeout(100) alone')
    try {
        const signal = AbortSignal.timeout(100)
        const start = Date.now()
        
        await new Promise((resolve, reject) => {
            signal.addEventListener('abort', () => {
                const elapsed = Date.now() - start
                console.log(`✅ AbortSignal.timeout fired after ${elapsed}ms`)
                reject(new Error('Timeout'))
            })
            
            // Simulate long operation
            setTimeout(() => {
                console.log('❌ Operation completed (should not happen)')
                resolve('done')
            }, 1000)
        })
    } catch (error) {
        console.log('🔍 Caught error:', (error as Error).message)
    }
    console.log('')
    
    // Test 2: AbortSignal.any with timeout
    console.log('🧪 Test 2: AbortSignal.any with timeout')
    try {
        const timeoutSignal = AbortSignal.timeout(100)
        const manualController = new AbortController()
        const combinedSignal = AbortSignal.any([timeoutSignal, manualController.signal])
        
        const start = Date.now()
        
        await new Promise((resolve, reject) => {
            combinedSignal.addEventListener('abort', () => {
                const elapsed = Date.now() - start
                console.log(`✅ Combined signal fired after ${elapsed}ms`)
                reject(new Error('Timeout'))
            })
            
            // Simulate long operation
            setTimeout(() => {
                console.log('❌ Operation completed (should not happen)')
                resolve('done')
            }, 1000)
        })
    } catch (error) {
        console.log('🔍 Caught error:', (error as Error).message)
    }
    console.log('')
    
    // Test 3: Test with actual HTTP request using undici directly
    console.log('🧪 Test 3: Undici with AbortSignal.timeout')
    
    const server = createServer((req, res) => {
        console.log('📡 Server received request - not responding')
        // Don't respond
    })
    
    server.listen(0)
    await once(server, 'listening')
    const port = (server.address() as any)?.port
    
    try {
        const { request } = await import('undici')
        const signal = AbortSignal.timeout(100)
        const start = Date.now()
        
        await request(`http://localhost:${port}/timeout`, { signal })
        console.log('❌ Request completed (should not happen)')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Undici request failed after ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
    }
    
    server.close()
    await once(server, 'close')
    console.log('🧹 Server closed')
}

testAbortSignalTimeout().catch(console.error)
