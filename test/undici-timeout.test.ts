#!/usr/bin/env node

import { createServer } from 'http'
import { once } from 'events'
import { Pool } from 'undici'

async function testUndiciTimeout() {
    console.log('🔍 Testing Undici timeout behavior\n')
    
    const server = createServer((req, res) => {
        console.log('📡 Server received request - not responding')
        // Don't respond to simulate timeout
    })
    
    server.listen(0)
    await once(server, 'listening')
    const port = (server.address() as any)?.port
    const origin = `http://localhost:${port}`
    
    // Test 1: Undici request with AbortSignal.timeout
    console.log('🧪 Test 1: Undici request() with AbortSignal.timeout')
    try {
        const { request } = await import('undici')
        const signal = AbortSignal.timeout(100)
        const startTime = Date.now()
        
        await request(`${origin}/timeout`, { signal })
        console.log('❌ Request completed (should not happen)')
    } catch (error) {
        const elapsed = Date.now() - startTime
        console.log(`✅ Undici request failed after ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
    }
    console.log('')
    
    // Test 2: Undici Pool with AbortSignal.timeout
    console.log('🧪 Test 2: Undici Pool with AbortSignal.timeout')
    try {
        const pool = new Pool(origin)
        const signal = AbortSignal.timeout(100)
        const startTime = Date.now()
        
        await pool.request({
            path: '/timeout',
            method: 'POST',
            signal
        })
        console.log('❌ Pool request completed (should not happen)')
    } catch (error) {
        const elapsed = Date.now() - startTime
        console.log(`✅ Pool request failed after ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
    }
    console.log('')
    
    // Test 3: Pool with headersTimeout
    console.log('🧪 Test 3: Pool with headersTimeout')
    try {
        const pool = new Pool(origin, {
            headersTimeout: 100,
            bodyTimeout: 100
        })
        const startTime = Date.now()
        
        await pool.request({
            path: '/timeout',
            method: 'POST'
        })
        console.log('❌ Pool with timeout completed (should not happen)')
    } catch (error) {
        const elapsed = Date.now() - startTime
        console.log(`✅ Pool with timeout failed after ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
    }
    console.log('')
    
    // Test 4: AbortSignal.any behavior
    console.log('🧪 Test 4: AbortSignal.any with timeout and manual signal')
    try {
        const timeoutSignal = AbortSignal.timeout(100)
        const manualController = new AbortController()
        const combinedSignal = AbortSignal.any([timeoutSignal, manualController.signal])
        
        const pool = new Pool(origin)
        const startTime = Date.now()
        
        await pool.request({
            path: '/timeout',
            method: 'POST',
            signal: combinedSignal
        })
        console.log('❌ Combined signal request completed (should not happen)')
    } catch (error) {
        const elapsed = Date.now() - startTime
        console.log(`✅ Combined signal failed after ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
    }
    console.log('')
    
    server.close()
    await once(server, 'close')
    console.log('🧹 Server closed')
}

testUndiciTimeout().catch(console.error)
