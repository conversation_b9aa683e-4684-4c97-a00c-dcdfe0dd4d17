#!/usr/bin/env node

import { createServer } from 'http'
import { strict as assert } from 'assert'
import { once } from 'events'
import { Sender } from '../app/utils/sender/sender.js'
import { HttpMethod } from '../app/utils/sender/constants.js'

// Test utilities
class TestRunner {
    constructor() {
        this.tests = []
        this.passed = 0
        this.failed = 0
        this.server = null
        this.serverPort = null
    }

    test(name, fn) {
        this.tests.push({ name, fn })
    }

    async setupServer() {
        this.server = createServer((req, res) => {
            const url = new URL(req.url, `http://localhost:${this.serverPort}`)
            const path = url.pathname
            const delay = parseInt(url.searchParams.get('delay') || '0')

            setTimeout(() => {
                if (path === '/success') {
                    res.writeHead(200, { 'Content-Type': 'application/json' })
                    res.end(JSON.stringify({ message: 'success', method: req.method }))
                } else if (path === '/error') {
                    res.writeHead(500, { 'Content-Type': 'application/json' })
                    res.end(JSON.stringify({ error: 'Internal Server Error' }))
                } else if (path === '/timeout') {
                    // Don't respond to simulate timeout
                    return
                } else if (path === '/retry') {
                    const attempt = parseInt(url.searchParams.get('attempt') || '1')
                    if (attempt < 3) {
                        res.writeHead(500, { 'Content-Type': 'application/json' })
                        res.end(JSON.stringify({ error: 'Retry me', attempt }))
                    } else {
                        res.writeHead(200, { 'Content-Type': 'application/json' })
                        res.end(JSON.stringify({ message: 'success after retry', attempt }))
                    }
                } else if (path === '/echo') {
                    let body = ''
                    req.on('data', chunk => body += chunk)
                    req.on('end', () => {
                        res.writeHead(200, { 'Content-Type': 'application/json' })
                        res.end(JSON.stringify({
                            method: req.method,
                            headers: req.headers,
                            body: body || null
                        }))
                    })
                } else if (path === '/large') {
                    const size = parseInt(url.searchParams.get('size') || '1000')
                    res.writeHead(200, { 'Content-Type': 'text/plain' })
                    res.end('x'.repeat(size))
                } else {
                    res.writeHead(404, { 'Content-Type': 'application/json' })
                    res.end(JSON.stringify({ error: 'Not Found' }))
                }
            }, delay)
        })

        this.server.listen(0)
        await once(this.server, 'listening')
        this.serverPort = this.server.address().port
    }

    async teardownServer() {
        if (this.server) {
            this.server.close()
            await once(this.server, 'close')
        }
    }

    async run() {
        console.log('🧪 Starting Sender Tests...\n')
        
        await this.setupServer()
        console.log(`📡 Test server running on port ${this.serverPort}\n`)

        for (const { name, fn } of this.tests) {
            try {
                console.log(`🔍 ${name}`)
                await fn()
                this.passed++
                console.log(`✅ PASSED\n`)
            } catch (error) {
                this.failed++
                console.log(`❌ FAILED: ${error.message}\n`)
                console.error(error.stack)
            }
        }

        await this.teardownServer()

        console.log(`\n📊 Test Results:`)
        console.log(`✅ Passed: ${this.passed}`)
        console.log(`❌ Failed: ${this.failed}`)
        console.log(`📈 Total: ${this.tests.length}`)
        
        if (this.failed > 0) {
            process.exit(1)
        }
    }

    getServerUrl(path = '/') {
        return `http://localhost:${this.serverPort}${path}`
    }
}

const runner = new TestRunner()

// Constructor Tests
runner.test('Constructor - Valid URL parsing', async () => {
    const sender = new Sender('http://example.com/api/test')
    assert.equal(sender.origin, 'http://example.com')
    assert.equal(sender.path, '/api/test')
})

runner.test('Constructor - Invalid URL throws error', async () => {
    assert.throws(() => {
        new Sender('invalid-url')
    }, /Failed to parse URL/)
})

runner.test('Constructor - Default options', async () => {
    const sender = new Sender('http://example.com')
    assert.equal(sender.activeRequests, 0)
    assert.equal(typeof sender.connections, 'number')
})

runner.test('Constructor - Custom options', async () => {
    const sender = new Sender('http://example.com', {
        timeout: 5000,
        method: HttpMethod.GET,
        headers: { 'User-Agent': 'test' }
    })
    assert.equal(sender.origin, 'http://example.com')
})

// Basic Request Tests
runner.test('Basic GET request', async () => {
    const sender = new Sender(runner.getServerUrl('/success'))
    const response = await sender.send()
    
    assert.equal(response.status, 200)
    assert.equal(typeof response.id, 'string')
    assert.equal(typeof response.took, 'bigint')
    assert(response.took > 0n)
    
    const body = JSON.parse(response.body)
    assert.equal(body.message, 'success')
})

runner.test('POST request with body', async () => {
    const sender = new Sender(runner.getServerUrl('/echo'), {
        method: HttpMethod.POST
    })
    
    const testBody = JSON.stringify({ test: 'data' })
    const response = await sender.send(testBody)
    
    assert.equal(response.status, 200)
    const responseBody = JSON.parse(response.body)
    assert.equal(responseBody.method, 'POST')
    assert.equal(responseBody.body, testBody)
})

runner.test('Custom headers', async () => {
    const sender = new Sender(runner.getServerUrl('/echo'), {
        headers: { 'X-Test-Header': 'test-value' }
    })
    
    const response = await sender.send()
    const body = JSON.parse(response.body)
    assert.equal(body.headers['x-test-header'], 'test-value')
})

runner.test('Per-request options override', async () => {
    const sender = new Sender(runner.getServerUrl('/echo'), {
        method: HttpMethod.GET,
        headers: { 'X-Default': 'default' }
    })
    
    const response = await sender.send('test body', {
        method: HttpMethod.POST,
        headers: { 'X-Override': 'override' }
    })
    
    const body = JSON.parse(response.body)
    assert.equal(body.method, 'POST')
    assert.equal(body.headers['x-default'], 'default')
    assert.equal(body.headers['x-override'], 'override')
    assert.equal(body.body, 'test body')
})

// Event System Tests
runner.test('Event system - activeRequests', async () => {
    const sender = new Sender(runner.getServerUrl('/success'))
    let activeRequestsEvents = []
    
    sender.on('activeRequests', (count) => {
        activeRequestsEvents.push(count)
    })
    
    const promise = sender.send()
    await promise
    
    assert.deepEqual(activeRequestsEvents, [1, 0])
})

runner.test('Event system - request and response events', async () => {
    const sender = new Sender(runner.getServerUrl('/success'))
    let requestEvent = null
    let responseEvent = null
    
    sender.on('request', (req) => { requestEvent = req })
    sender.on('response', (res) => { responseEvent = res })
    
    await sender.send('test')
    
    assert(requestEvent)
    assert.equal(requestEvent.body, 'test')
    assert.equal(typeof requestEvent.id, 'string')
    
    assert(responseEvent)
    assert.equal(responseEvent.status, 200)
    assert.equal(responseEvent.id, requestEvent.id)
})

// Error Handling Tests
runner.test('HTTP error handling', async () => {
    const sender = new Sender(runner.getServerUrl('/error'))
    let errorEvent = null
    
    sender.on('error', (err) => { errorEvent = err })
    
    try {
        await sender.send()
        assert.fail('Should have thrown an error')
    } catch (error) {
        assert(error.message.includes('Request failed'))
        assert(errorEvent)
        assert.equal(errorEvent.constructor.name, 'SenderRequestError')
    }
})

runner.test('Timeout handling', async () => {
    const sender = new Sender(runner.getServerUrl('/timeout'), {
        timeout: 100
    })
    
    const start = Date.now()
    try {
        await sender.send()
        assert.fail('Should have thrown timeout error')
    } catch (error) {
        const elapsed = Date.now() - start
        assert(elapsed >= 100 && elapsed < 200, `Timeout should be ~100ms, got ${elapsed}ms`)
    }
})

runner.test('AbortSignal support', async () => {
    const sender = new Sender(runner.getServerUrl('/success?delay=1000'))
    const controller = new AbortController()
    
    setTimeout(() => controller.abort(), 100)
    
    try {
        await sender.send(null, { signal: controller.signal })
        assert.fail('Should have been aborted')
    } catch (error) {
        assert(error.name === 'AbortError' || error.message.includes('aborted'))
    }
})

// Continue with more tests...
