#!/usr/bin/env node

import { createServer } from 'http'
import { once } from 'events'
import { Sender } from '../app/utils/sender/sender'

async function testTimeoutWithoutDNS() {
    console.log('🔍 Testing Timeout with DNS Cache DISABLED\n')
    
    // Setup test server
    const server = createServer((req, res) => {
        console.log(`📡 Server received: ${req.method} ${req.url}`)
        
        if (req.url?.includes('/timeout')) {
            console.log('⏰ Timeout endpoint - not responding (simulating hang)')
            // Don't respond to simulate timeout
            return
        }
        
        res.writeHead(200, { 'Content-Type': 'application/json' })
        res.end(JSON.stringify({ message: 'success' }))
    })
    
    server.listen(0)
    await once(server, 'listening')
    const port = (server.address() as any)?.port
    console.log(`📡 Test server running on port ${port}\n`)
    
    // Test 1: Timeout with DNS cache DISABLED
    console.log('🧪 Test 1: Timeout 100ms with DNS cache DISABLED')
    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            timeout: 100,
            retry: false,
            dns: { enabled: false }  // DISABLE DNS cache
        })
        
        const start = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Timeout with DNS disabled after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
        console.log('🔍 Error cause name:', (error as any).cause?.name)
        console.log('🔍 Within expected range (90-150ms):', elapsed >= 90 && elapsed <= 150)
    }
    console.log('')
    
    // Test 2: Compare with DNS cache ENABLED (default)
    console.log('🧪 Test 2: Timeout 100ms with DNS cache ENABLED (default)')
    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            timeout: 100,
            retry: false,
            dns: { enabled: true }  // ENABLE DNS cache (default)
        })
        
        const start = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Timeout with DNS enabled after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
        console.log('🔍 Much longer than expected:', elapsed > 200)
    }
    console.log('')
    
    // Test 3: DNS cache disabled with boolean
    console.log('🧪 Test 3: DNS cache disabled with boolean (dns: false)')
    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            timeout: 100,
            retry: false,
            dns: false  // Alternative way to disable
        })
        
        const start = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Timeout with dns: false after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Within expected range (90-150ms):', elapsed >= 90 && elapsed <= 150)
    }
    console.log('')
    
    // Test 4: Very short timeout with DNS disabled
    console.log('🧪 Test 4: Very short timeout (50ms) with DNS disabled')
    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            timeout: 50,
            retry: false,
            dns: false
        })
        
        const start = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Short timeout with DNS disabled after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Within expected range (40-80ms):', elapsed >= 40 && elapsed <= 80)
    }
    console.log('')
    
    // Test 5: Successful request with DNS disabled (sanity check)
    console.log('🧪 Test 5: Successful request with DNS disabled')
    try {
        const sender = new Sender(`http://localhost:${port}/success`, {
            timeout: 1000,
            retry: false,
            dns: false
        })
        
        const start = Date.now()
        const response = await sender.send()
        const elapsed = Date.now() - start
        
        console.log(`✅ Successful request with DNS disabled: ${elapsed}ms`)
        console.log('🔍 Response status:', response.status)
        console.log('🔍 Response body:', response.body)
        console.log('🔍 Fast response (< 50ms):', elapsed < 50)
    } catch (error) {
        console.log('❌ ERROR: Successful request failed:', (error as Error).message)
    }
    console.log('')
    
    // Test 6: Successful request with DNS enabled (comparison)
    console.log('🧪 Test 6: Successful request with DNS enabled')
    try {
        const sender = new Sender(`http://localhost:${port}/success`, {
            timeout: 1000,
            retry: false,
            dns: true
        })
        
        const start = Date.now()
        const response = await sender.send()
        const elapsed = Date.now() - start
        
        console.log(`✅ Successful request with DNS enabled: ${elapsed}ms`)
        console.log('🔍 Response status:', response.status)
        console.log('🔍 Response body:', response.body)
        console.log('🔍 Fast response (< 50ms):', elapsed < 50)
    } catch (error) {
        console.log('❌ ERROR: Successful request failed:', (error as Error).message)
    }
    console.log('')
    
    // Test 7: External domain with DNS cache disabled (real DNS test)
    console.log('🧪 Test 7: External domain timeout with DNS disabled')
    try {
        const sender = new Sender('http://non-existent-domain-12345.com/test', {
            timeout: 100,
            retry: false,
            dns: false
        })
        
        const start = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have failed!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ External domain failed after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
        console.log('🔍 DNS resolution timeout or error:', elapsed <= 200)
    }
    console.log('')
    
    // Cleanup
    server.close()
    await once(server, 'close')
    console.log('🧹 Test server closed')
    
    console.log('\n📊 Summary:')
    console.log('- DNS cache DISABLED should allow proper timeout behavior')
    console.log('- DNS cache ENABLED may interfere with AbortSignal timeout')
    console.log('- This will confirm if DNS interceptor is the root cause')
}

testTimeoutWithoutDNS().catch(console.error)
