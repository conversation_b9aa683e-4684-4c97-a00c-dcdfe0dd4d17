# Sender Class Test Suite

Comprehensive test suite cho class `Sender` được thiết kế bởi một tester chuyên nghiệp với 10 năm kinh nghiệm.

## 📋 Test Coverage

### 1. **Constructor & Configuration Tests**
- ✅ Valid URL parsing
- ✅ Invalid URL error handling
- ✅ Default options validation
- ✅ Custom options configuration
- ✅ Pool options setup
- ✅ DNS cache configuration

### 2. **Basic HTTP Operations**
- ✅ GET requests
- ✅ POST requests with body
- ✅ All HTTP methods (PUT, DELETE, PATCH, etc.)
- ✅ Custom headers handling
- ✅ Per-request options override
- ✅ Request body variations (null, undefined, empty string)

### 3. **Event System Tests**
- ✅ activeRequests event tracking
- ✅ connections event monitoring
- ✅ request/response event lifecycle
- ✅ error event handling

### 4. **Error Handling Tests**
- ✅ HTTP error responses (4xx, 5xx)
- ✅ Network errors
- ✅ Timeout handling
- ✅ AbortSignal support
- ✅ Error context preservation
- ✅ Custom error types

### 5. **Retry Logic Tests**
- ✅ Basic retry on errors
- ✅ Retry with custom conditions (`shouldRetry`)
- ✅ Response-based retry (`shouldRetryOnResponse`)
- ✅ Exponential backoff with jitter
- ✅ Retry callbacks (`onFailedAttempt`, `onResponseAttempt`)
- ✅ Complex retry scenarios

### 6. **Response Formatting Tests**
- ✅ Custom response body formatters
- ✅ JSON parsing
- ✅ Invalid JSON handling
- ✅ Type-safe response bodies

### 7. **Concurrency & Performance Tests**
- ✅ Concurrent requests handling
- ✅ Memory usage with large request volumes
- ✅ Response timing accuracy
- ✅ Stress testing (100+ sequential requests)
- ✅ Request ID uniqueness

### 8. **Connection Pooling Tests**
- ✅ Connection reuse verification
- ✅ Pool statistics monitoring
- ✅ Keep-alive behavior

### 9. **DNS Caching Tests**
- ✅ DNS cache enabled/disabled
- ✅ Custom DNS cache options
- ✅ DNS interceptor configuration

### 10. **Advanced Edge Cases**
- ✅ Large payload handling
- ✅ Headers case sensitivity and merging
- ✅ Response header parsing
- ✅ Multiple AbortSignals
- ✅ Invalid JSON responses
- ✅ Request metadata preservation

## 🚀 How to Run Tests

### Prerequisites
```bash
# Ensure you have Node.js 18+ and TypeScript installed
npm install -g typescript
```

### Running the Tests

#### Option 1: Direct TypeScript execution
```bash
# From project root
npx tsx test/sender.test.ts
```

#### Option 2: Compile and run
```bash
# Compile TypeScript
npx tsc test/sender.test.ts --target es2022 --module node16 --moduleResolution node16

# Run compiled JavaScript
node test/sender.test.js
```

#### Option 3: Using ts-node
```bash
npx ts-node test/sender.test.ts
```

## 📊 Expected Output

```
🚀 Running Comprehensive Sender Tests...
📋 Test Categories:
  • Constructor & Configuration
  • Basic HTTP Operations
  • Event System
  • Error Handling
  • Retry Logic
  • Response Formatting
  • Concurrency & Performance
  • Connection Pooling
  • DNS Caching
  • Edge Cases & Stress Tests

📡 Test server running on port 12345

🔍 Constructor - Valid URL parsing
✅ PASSED

🔍 Constructor - Invalid URL throws error
✅ PASSED

... (more tests)

📊 Test Results:
✅ Passed: 45
❌ Failed: 0
📈 Total: 45
```

## 🧪 Test Architecture

### Test Runner
- **Custom test runner** không sử dụng framework
- **HTTP test server** tự động setup/teardown
- **Comprehensive assertions** với Node.js built-in assert
- **Detailed error reporting** với stack traces

### Test Server Endpoints
- `/success` - Returns 200 OK
- `/error` - Returns 500 Internal Server Error
- `/timeout` - Không response (để test timeout)
- `/retry` - Conditional retry behavior
- `/echo` - Echo request details
- `/large` - Large response payloads
- `/json` - JSON response data
- `/custom-headers` - Custom response headers
- `/invalid-json` - Invalid JSON response

### Test Categories

#### Unit Tests
- Individual method testing
- Configuration validation
- Error handling

#### Integration Tests
- End-to-end request/response cycles
- Event system integration
- Retry mechanism integration

#### Performance Tests
- Concurrent request handling
- Memory usage validation
- Timing accuracy

#### Stress Tests
- High-volume sequential requests
- Large payload handling
- Resource cleanup verification

## 🔧 Customization

### Adding New Tests
```typescript
runner.test('Your test name', async () => {
    const sender = new Sender('http://example.com')
    const response = await sender.send()
    assert.equal(response.status, 200)
})
```

### Custom Test Server Endpoints
Modify the server request handler in `TestRunner.setupServer()` to add new endpoints.

### Test Configuration
Adjust test parameters like timeouts, retry counts, and payload sizes in individual test cases.

## 📝 Notes

- Tests run in **isolated environment** với dedicated test server
- **No external dependencies** - tất cả tests chạy locally
- **Comprehensive coverage** - test tất cả public APIs và edge cases
- **Production-ready validation** - test các scenarios thực tế
- **Performance benchmarking** - đo lường timing và resource usage

## 🐛 Troubleshooting

### Common Issues

1. **Port conflicts**: Test server tự động chọn available port
2. **Timeout errors**: Increase timeout values nếu system chậm
3. **Memory issues**: Giảm số lượng concurrent requests trong stress tests
4. **TypeScript errors**: Ensure proper imports và type definitions

### Debug Mode
Thêm console.log statements trong test cases để debug:

```typescript
runner.test('Debug test', async () => {
    console.log('Starting test...')
    const sender = new Sender(runner.getServerUrl('/success'))
    console.log('Sender created')
    const response = await sender.send()
    console.log('Response:', response)
})
```
