#!/usr/bin/env node

import { createServer } from 'http'
import { once } from 'events'
import { Pool } from 'undici'

async function testPoolRequestOptions() {
    console.log('🔍 Testing Pool.request() options\n')
    
    // Setup test server
    const server = createServer((req, res) => {
        console.log(`📡 Server received: ${req.method} ${req.url}`)
        
        if (req.url?.includes('/timeout')) {
            console.log('⏰ Timeout endpoint - not responding')
            // Don't respond to simulate timeout
            return
        }
        
        res.writeHead(200, { 'Content-Type': 'application/json' })
        res.end(JSON.stringify({ message: 'success' }))
    })
    
    server.listen(0)
    await once(server, 'listening')
    const port = (server.address() as any)?.port
    const origin = `http://localhost:${port}`
    console.log(`📡 Test server running on port ${port}\n`)
    
    // Test 1: Pool.request() with path only (normal pattern)
    console.log('🧪 Test 1: Pool.request() with path only')
    try {
        const pool = new Pool(origin)
        const signal = AbortSignal.timeout(100)
        const start = Date.now()
        
        await pool.request({
            path: '/timeout',
            method: 'POST',
            signal
        })
        console.log('❌ Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Normal pattern timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
    }
    console.log('')
    
    // Test 2: Pool.request() with origin parameter (Sender pattern)
    console.log('🧪 Test 2: Pool.request() with origin parameter (Sender pattern)')
    try {
        const pool = new Pool(origin)
        const signal = AbortSignal.timeout(100)
        const start = Date.now()
        
        await pool.request({
            origin: origin,  // This is what Sender does!
            path: '/timeout',
            method: 'POST',
            blocking: false,
            signal
        })
        console.log('❌ Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Sender pattern timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Within expected range:', elapsed >= 90 && elapsed <= 150)
    }
    console.log('')
    
    // Test 3: Pool.request() with blocking: false
    console.log('🧪 Test 3: Pool.request() with blocking: false only')
    try {
        const pool = new Pool(origin)
        const signal = AbortSignal.timeout(100)
        const start = Date.now()
        
        await pool.request({
            path: '/timeout',
            method: 'POST',
            blocking: false,  // This might be the issue
            signal
        })
        console.log('❌ Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ blocking: false timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
    }
    console.log('')
    
    // Test 4: Pool.request() without blocking parameter
    console.log('🧪 Test 4: Pool.request() without blocking parameter')
    try {
        const pool = new Pool(origin)
        const signal = AbortSignal.timeout(100)
        const start = Date.now()
        
        await pool.request({
            path: '/timeout',
            method: 'POST',
            signal
            // No blocking parameter
        })
        console.log('❌ Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ No blocking timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
    }
    console.log('')
    
    // Test 5: Pool.request() with blocking: true
    console.log('🧪 Test 5: Pool.request() with blocking: true')
    try {
        const pool = new Pool(origin)
        const signal = AbortSignal.timeout(100)
        const start = Date.now()
        
        await pool.request({
            path: '/timeout',
            method: 'POST',
            blocking: true,
            signal
        })
        console.log('❌ Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ blocking: true timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
    }
    console.log('')
    
    // Test 6: Successful request to verify pool works
    console.log('🧪 Test 6: Successful request')
    try {
        const pool = new Pool(origin)
        const start = Date.now()
        
        const response = await pool.request({
            path: '/success',
            method: 'POST'
        })
        
        const elapsed = Date.now() - start
        console.log(`✅ Successful request: ${elapsed}ms`)
        console.log('🔍 Status:', response.statusCode)
        
        const body = await response.body.text()
        console.log('🔍 Body:', body)
    } catch (error) {
        console.log('❌ ERROR: Successful request failed:', (error as Error).message)
    }
    console.log('')
    
    // Cleanup
    server.close()
    await once(server, 'close')
    console.log('🧹 Test server closed')
}

testPoolRequestOptions().catch(console.error)
