#!/usr/bin/env node

async function testAbortSignalAny() {
    console.log('🔍 Testing AbortSignal.any() behavior\n')
    
    // Test 1: AbortSignal.timeout() alone
    console.log('🧪 Test 1: AbortSignal.timeout(100) alone')
    try {
        const signal = AbortSignal.timeout(100)
        const start = Date.now()
        
        await new Promise((resolve, reject) => {
            signal.addEventListener('abort', () => {
                const elapsed = Date.now() - start
                console.log(`✅ Direct timeout fired after ${elapsed}ms`)
                reject(new Error('Timeout'))
            })
            setTimeout(() => resolve('done'), 1000)
        })
    } catch (error) {
        console.log('🔍 Caught:', (error as Error).message)
    }
    console.log('')
    
    // Test 2: AbortSignal.any() with single timeout signal
    console.log('🧪 Test 2: AbortSignal.any([AbortSignal.timeout(100)])')
    try {
        const timeoutSignal = AbortSignal.timeout(100)
        const signal = AbortSignal.any([timeoutSignal])
        const start = Date.now()
        
        await new Promise((resolve, reject) => {
            signal.addEventListener('abort', () => {
                const elapsed = Date.now() - start
                console.log(`✅ AbortSignal.any with single timeout fired after ${elapsed}ms`)
                reject(new Error('Timeout'))
            })
            setTimeout(() => resolve('done'), 1000)
        })
    } catch (error) {
        console.log('🔍 Caught:', (error as Error).message)
    }
    console.log('')
    
    // Test 3: AbortSignal.any() with timeout + empty spread (simulating Sender code)
    console.log('🧪 Test 3: AbortSignal.any([AbortSignal.timeout(100), ...[]]) - Sender pattern')
    try {
        const timeoutSignal = AbortSignal.timeout(100)
        const optionalSignals: AbortSignal[] = [] // Simulating empty options.signal
        const signal = AbortSignal.any([timeoutSignal, ...optionalSignals])
        const start = Date.now()
        
        await new Promise((resolve, reject) => {
            signal.addEventListener('abort', () => {
                const elapsed = Date.now() - start
                console.log(`✅ Sender pattern fired after ${elapsed}ms`)
                reject(new Error('Timeout'))
            })
            setTimeout(() => resolve('done'), 1000)
        })
    } catch (error) {
        console.log('🔍 Caught:', (error as Error).message)
    }
    console.log('')
    
    // Test 4: AbortSignal.any() with timeout + manual signal
    console.log('🧪 Test 4: AbortSignal.any([timeout, manual]) - timeout should win')
    try {
        const timeoutSignal = AbortSignal.timeout(100)
        const manualController = new AbortController()
        const signal = AbortSignal.any([timeoutSignal, manualController.signal])
        const start = Date.now()
        
        await new Promise((resolve, reject) => {
            signal.addEventListener('abort', () => {
                const elapsed = Date.now() - start
                console.log(`✅ Combined signals fired after ${elapsed}ms`)
                reject(new Error('Timeout'))
            })
            setTimeout(() => resolve('done'), 1000)
        })
    } catch (error) {
        console.log('🔍 Caught:', (error as Error).message)
    }
    console.log('')
    
    // Test 5: Test with undici using different signal patterns
    console.log('🧪 Test 5: Undici with different signal patterns')
    
    const { createServer } = await import('http')
    const { once } = await import('events')
    const { request } = await import('undici')
    
    const server = createServer((req, res) => {
        console.log('📡 Server received request - hanging...')
        // Don't respond
    })
    
    server.listen(0)
    await once(server, 'listening')
    const port = (server.address() as any)?.port
    
    // Test 5a: Direct timeout signal
    console.log('📍 5a: Undici with direct AbortSignal.timeout(100)')
    try {
        const signal = AbortSignal.timeout(100)
        const start = Date.now()
        
        await request(`http://localhost:${port}/test`, { signal })
        console.log('❌ Should not reach here')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Direct signal: ${elapsed}ms - ${(error as any).name}`)
    }
    
    // Test 5b: AbortSignal.any with single timeout
    console.log('📍 5b: Undici with AbortSignal.any([timeout])')
    try {
        const timeoutSignal = AbortSignal.timeout(100)
        const signal = AbortSignal.any([timeoutSignal])
        const start = Date.now()
        
        await request(`http://localhost:${port}/test`, { signal })
        console.log('❌ Should not reach here')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Any single: ${elapsed}ms - ${(error as any).name}`)
    }
    
    // Test 5c: AbortSignal.any with timeout + empty spread (exact Sender pattern)
    console.log('📍 5c: Undici with Sender exact pattern')
    try {
        const timeout = 100
        const optionsSignal = undefined // No signal provided
        const signal = AbortSignal.any([AbortSignal.timeout(timeout), ...(optionsSignal ? [optionsSignal] : [])])
        const start = Date.now()
        
        await request(`http://localhost:${port}/test`, { signal })
        console.log('❌ Should not reach here')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Exact Sender pattern: ${elapsed}ms - ${(error as any).name}`)
    }
    
    server.close()
    await once(server, 'close')
    console.log('🧹 Server closed')
}

testAbortSignalAny().catch(console.error)
