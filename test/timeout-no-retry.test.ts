#!/usr/bin/env node

import { createServer } from 'http'
import { once } from 'events'
import { Sender } from '../app/utils/sender/sender'

async function testTimeoutWithoutRetry() {
    console.log('🔍 Testing Timeout with Retry DISABLED\n')
    
    // Setup test server
    const server = createServer((req, res) => {
        console.log(`📡 Server received: ${req.method} ${req.url}`)
        
        if (req.url?.includes('/timeout')) {
            console.log('⏰ Timeout endpoint - not responding (simulating hang)')
            // Don't respond to simulate timeout
            return
        }
        
        res.writeHead(200, { 'Content-Type': 'application/json' })
        res.end(JSON.stringify({ message: 'success' }))
    })
    
    server.listen(0)
    await once(server, 'listening')
    const port = (server.address() as any)?.port
    console.log(`📡 Test server running on port ${port}\n`)
    
    // Test 1: Timeout with retry DISABLED
    console.log('🧪 Test 1: Timeout 100ms with retry DISABLED')
    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            timeout: 100,
            retry: false  // DISABLE retry
        })
        
        const start = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Error message:', (error as Error).message)
        console.log('🔍 Error cause name:', (error as any).cause?.name)
        console.log('🔍 Error cause message:', (error as any).cause?.message)
        console.log('🔍 Within expected range (90-150ms):', elapsed >= 90 && elapsed <= 150)
    }
    console.log('')
    
    // Test 2: Very short timeout with retry disabled
    console.log('🧪 Test 2: Timeout 50ms with retry DISABLED')
    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            timeout: 50,
            retry: { enabled: false }  // Alternative way to disable
        })
        
        const start = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Short timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Within expected range (40-80ms):', elapsed >= 40 && elapsed <= 80)
    }
    console.log('')
    
    // Test 3: Compare with retry ENABLED (for reference)
    console.log('🧪 Test 3: Timeout 100ms with retry ENABLED (for comparison)')
    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            timeout: 100,
            retry: {
                enabled: true,
                retries: 2,
                delay: 10
            }
        })
        
        const start = Date.now()
        await sender.send()
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Timeout with retry after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Much longer than 100ms:', elapsed > 200)
    }
    console.log('')
    
    // Test 4: AbortSignal timeout directly (no retry involved)
    console.log('🧪 Test 4: Direct AbortSignal timeout (bypassing Sender retry)')
    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            retry: false
        })
        
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 100)
        
        const start = Date.now()
        await sender.send(null, { signal: controller.signal })
        clearTimeout(timeoutId)
        console.log('❌ ERROR: Should have been aborted!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Manual abort after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Within expected range (90-150ms):', elapsed >= 90 && elapsed <= 150)
    }
    console.log('')
    
    // Test 5: Per-request timeout override with retry disabled
    console.log('🧪 Test 5: Per-request timeout override (75ms) with retry disabled')
    try {
        const sender = new Sender(`http://localhost:${port}/timeout`, {
            timeout: 1000,  // Default high timeout
            retry: false
        })
        
        const start = Date.now()
        await sender.send(null, {
            timeout: 75,  // Override with shorter timeout
            retry: false  // Ensure retry is disabled for this request too
        })
        console.log('❌ ERROR: Should have timed out!')
    } catch (error) {
        const elapsed = Date.now() - start
        console.log(`✅ Override timeout after: ${elapsed}ms`)
        console.log('🔍 Error name:', (error as any).name)
        console.log('🔍 Within expected range (65-100ms):', elapsed >= 65 && elapsed <= 100)
    }
    console.log('')
    
    // Test 6: Successful request to verify normal operation
    console.log('🧪 Test 6: Successful request (sanity check)')
    try {
        const sender = new Sender(`http://localhost:${port}/success`, {
            timeout: 100,
            retry: false
        })
        
        const start = Date.now()
        const response = await sender.send()
        const elapsed = Date.now() - start
        
        console.log(`✅ Successful request completed in: ${elapsed}ms`)
        console.log('🔍 Response status:', response.status)
        console.log('🔍 Response body:', response.body)
        console.log('🔍 Fast response (< 50ms):', elapsed < 50)
    } catch (error) {
        console.log('❌ ERROR: Successful request failed:', (error as Error).message)
    }
    console.log('')
    
    // Cleanup
    server.close()
    await once(server, 'close')
    console.log('🧹 Test server closed')
    
    console.log('\n📊 Summary:')
    console.log('- Timeout with retry DISABLED should be ~100ms')
    console.log('- Timeout with retry ENABLED will be much longer (multiple attempts)')
    console.log('- This confirms that retry logic affects timeout behavior')
}

testTimeoutWithoutRetry().catch(console.error)
