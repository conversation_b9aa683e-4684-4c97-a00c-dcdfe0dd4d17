#!/usr/bin/env node

import { createServer } from 'http'
import { once } from 'events'

async function minimalTimeoutTest() {
    console.log('🔍 Minimal Timeout Test\n')
    
    // Setup test server that never responds
    const server = createServer((req, res) => {
        console.log(`📡 Server received request - hanging...`)
        // Don't respond - simulate timeout
    })
    
    server.listen(0)
    await once(server, 'listening')
    const port = (server.address() as any)?.port
    console.log(`📡 Test server running on port ${port}\n`)
    
    // Test 1: Pure AbortSignal.timeout
    console.log('🧪 Test 1: Pure AbortSignal.timeout(100)')
    try {
        const signal = AbortSignal.timeout(100)
        const startTime = Date.now()
        
        await new Promise((resolve, reject) => {
            signal.addEventListener('abort', () => {
                const elapsed = Date.now() - startTime
                console.log(`✅ AbortSignal fired after ${elapsed}ms`)
                reject(new Error('Timeout'))
            })
            
            // Simulate long operation
            setTimeout(() => resolve('done'), 1000)
        })
        
        console.log('❌ Should not reach here')
    } catch (error) {
        console.log('🔍 Caught:', (error as Error).message)
    }
    console.log('')
    
    // Test 2: Undici with timeout
    console.log('🧪 Test 2: Undici request with timeout')
    try {
        const { request } = await import('undici')
        const signal = AbortSignal.timeout(100)
        const startTime = Date.now()
        
        await request(`http://localhost:${port}/test`, { signal })
        console.log('❌ Should not reach here')
    } catch (error) {
        const elapsed = Date.now() - startTime
        console.log(`✅ Undici failed after ${elapsed}ms`)
        console.log('🔍 Error:', (error as any).name, '-', (error as Error).message)
    }
    console.log('')
    
    // Test 3: Fetch with timeout (for comparison)
    console.log('🧪 Test 3: Fetch with AbortSignal.timeout')
    try {
        const signal = AbortSignal.timeout(100)
        const startTime = Date.now()
        
        await fetch(`http://localhost:${port}/test`, { signal })
        console.log('❌ Should not reach here')
    } catch (error) {
        const elapsed = Date.now() - startTime
        console.log(`✅ Fetch failed after ${elapsed}ms`)
        console.log('🔍 Error:', (error as any).name, '-', (error as Error).message)
    }
    console.log('')
    
    // Cleanup
    server.close()
    await once(server, 'close')
    console.log('🧹 Server closed')
}

minimalTimeoutTest().catch(console.error)
